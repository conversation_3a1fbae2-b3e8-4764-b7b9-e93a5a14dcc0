# Modern CRM Admin Panel

A modern, responsive admin panel built with Bootstrap 5, designed specifically for CRM applications. Features a clean UI, collapsible sidebar, login system, and comprehensive dashboard.

## Features

### 🎨 Modern UI Design
- Clean, professional interface with modern gradients
- Consistent color scheme and typography
- Smooth animations and transitions
- Card-based layout with subtle shadows

### 📱 Fully Responsive
- Mobile-first design approach
- Responsive sidebar that collapses on mobile
- Optimized for all screen sizes (desktop, tablet, mobile)
- Touch-friendly interface elements

### 🔐 Authentication System
- Beautiful login modal with form validation
- Forgot password functionality with email reset
- Remember me option
- Social login buttons (Google, Microsoft, Apple)
- Secure logout with confirmation

### 🎛️ Advanced Sidebar
- Collapsible/expandable sidebar
- Scrollable content with custom scrollbar
- Dropdown menus with smooth animations
- Active state indicators
- User profile section at bottom
- Logout button with styling

### 📊 Dashboard Features
- Statistics cards with animated counters
- Recent activity timeline
- Quick action buttons
- Notification system with badges
- Search functionality
- Breadcrumb navigation

### 🔔 Interactive Elements
- Toast notifications
- Dropdown menus
- Modal dialogs
- Form validation
- Loading states
- Hover effects

## File Structure

```
├── index.html              # Main dashboard page
├── login.html              # Standalone login page
├── forgot-password.html    # Password reset page
├── style.css               # Main stylesheet with modern design
├── script.js               # JavaScript functionality
└── README.md               # Documentation
```

## Technologies Used

- **Bootstrap 5.3.7** - CSS framework
- **Font Awesome 6.7.2** - Icons
- **Inter Font** - Typography
- **Vanilla JavaScript** - Functionality
- **CSS3** - Custom styling and animations

## Getting Started

1. **Clone or download** the project files
2. **Open `index.html`** in your browser to see the main dashboard
3. **Open `login.html`** for the standalone login page
4. **Open `forgot-password.html`** for the password reset page

## Usage

### Main Dashboard (`index.html`)
- Opens with a login modal on page load
- Use any email/password combination to login (demo mode)
- Explore the collapsible sidebar and responsive features
- Test the notification system and interactive elements

### Standalone Login (`login.html`)
- Clean, centered login form
- Password visibility toggle
- Social login options
- Redirects to dashboard on successful login

### Forgot Password (`forgot-password.html`)
- Email-based password reset
- Step-by-step instructions
- Success confirmation with resend option

## Customization

### Colors
The design uses CSS custom properties (variables) for easy customization:

```css
:root {
    --primary-color: #4f46e5;
    --primary-dark: #3730a3;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
}
```

### Sidebar Width
Adjust sidebar dimensions:

```css
:root {
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
}
```

### Responsive Breakpoints
The design is responsive with these breakpoints:
- **Desktop**: > 1200px
- **Laptop**: 992px - 1199px
- **Tablet**: 768px - 991px
- **Mobile**: < 768px

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers

## Features in Detail

### Sidebar Navigation
- **Collapsible**: Click the toggle button to expand/collapse
- **Responsive**: Automatically adapts to screen size
- **Scrollable**: Handles long navigation lists
- **Dropdowns**: Nested menu items with smooth animations
- **User Section**: Profile info and logout at bottom

### Dashboard Components
- **Stats Cards**: Animated counters with trend indicators
- **Activity Timeline**: Recent actions with icons and timestamps
- **Quick Actions**: Shortcut buttons for common tasks
- **Search Bar**: Global search functionality
- **Notifications**: Badge indicators and dropdown list

### Form Features
- **Validation**: Client-side form validation
- **Loading States**: Visual feedback during form submission
- **Password Toggle**: Show/hide password functionality
- **Remember Me**: Persistent login option

## Demo Credentials

For testing purposes, use any email and password combination. The system is in demo mode and will accept any valid email format.

## License

This project is open source and available under the MIT License.

## Support

For questions or support, please refer to the documentation or create an issue in the project repository.
