<!doctype html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>Forgot Password - CRM Admin Panel</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">
        
        <!-- Custom CSS -->
        <link rel="stylesheet" href="style.css">
        
        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha384-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
        
        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .forgot-container {
                max-width: 400px;
                width: 100%;
                padding: 2rem;
            }
            
            .forgot-card {
                background: white;
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                overflow: hidden;
                border: none;
            }
            
            .forgot-header {
                background: linear-gradient(135deg, #f59e0b, #d97706);
                color: white;
                text-align: center;
                padding: 2rem;
            }
            
            .forgot-header h2 {
                margin: 0;
                font-weight: 700;
            }
            
            .forgot-header p {
                margin: 0.5rem 0 0;
                opacity: 0.9;
                font-size: 0.9rem;
            }
            
            .forgot-body {
                padding: 2rem;
            }
            
            .info-text {
                text-align: center;
                color: #6b7280;
                margin-bottom: 2rem;
                line-height: 1.6;
            }
            
            .form-floating {
                margin-bottom: 1.5rem;
            }
            
            .form-floating .form-control {
                border: 2px solid #e5e7eb;
                border-radius: 12px;
                padding: 1rem 0.75rem;
                height: auto;
            }
            
            .form-floating .form-control:focus {
                border-color: #f59e0b;
                box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
            }
            
            .form-floating label {
                padding: 1rem 0.75rem;
                color: #6b7280;
            }
            
            .btn-reset {
                background: linear-gradient(135deg, #f59e0b, #d97706);
                border: none;
                border-radius: 12px;
                padding: 1rem;
                font-weight: 600;
                width: 100%;
                color: white;
                transition: all 0.3s ease;
            }
            
            .btn-reset:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 20px rgba(245, 158, 11, 0.3);
                color: white;
            }
            
            .back-to-login {
                text-align: center;
                margin-top: 1.5rem;
            }
            
            .back-to-login a {
                color: #4f46e5;
                text-decoration: none;
                font-weight: 500;
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
            }
            
            .back-to-login a:hover {
                text-decoration: underline;
            }
            
            .success-message {
                display: none;
                text-align: center;
                padding: 2rem;
            }
            
            .success-icon {
                width: 80px;
                height: 80px;
                background: linear-gradient(135deg, #10b981, #059669);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 1rem;
                color: white;
                font-size: 2rem;
            }
            
            .steps {
                background: #f9fafb;
                border-radius: 12px;
                padding: 1.5rem;
                margin-top: 1.5rem;
            }
            
            .steps h6 {
                color: #374151;
                margin-bottom: 1rem;
                font-weight: 600;
            }
            
            .steps ol {
                margin: 0;
                padding-left: 1.2rem;
                color: #6b7280;
            }
            
            .steps li {
                margin-bottom: 0.5rem;
                line-height: 1.5;
            }
        </style>
    </head>
    <body>
        <div class="forgot-container">
            <div class="card forgot-card">
                <div class="forgot-header">
                    <i class="fas fa-key fa-3x mb-3"></i>
                    <h2>Reset Password</h2>
                    <p>We'll help you get back into your account</p>
                </div>
                
                <div class="forgot-body">
                    <div id="resetForm">
                        <div class="info-text">
                            <p>Enter your email address and we'll send you a link to reset your password.</p>
                        </div>
                        
                        <form id="forgotPasswordForm">
                            <div class="form-floating">
                                <input type="email" class="form-control" id="resetEmail" placeholder="<EMAIL>" required>
                                <label for="resetEmail"><i class="fas fa-envelope me-2"></i>Email address</label>
                            </div>
                            
                            <button type="submit" class="btn btn-reset">
                                <i class="fas fa-paper-plane me-2"></i>Send Reset Link
                            </button>
                        </form>
                        
                        <div class="steps">
                            <h6><i class="fas fa-info-circle me-2"></i>What happens next?</h6>
                            <ol>
                                <li>We'll send a password reset link to your email</li>
                                <li>Click the link in the email to open the reset page</li>
                                <li>Enter your new password and confirm it</li>
                                <li>Sign in with your new password</li>
                            </ol>
                        </div>
                        
                        <div class="back-to-login">
                            <a href="login.html">
                                <i class="fas fa-arrow-left"></i>
                                Back to Login
                            </a>
                        </div>
                    </div>
                    
                    <div id="successMessage" class="success-message">
                        <div class="success-icon">
                            <i class="fas fa-check"></i>
                        </div>
                        <h4>Email Sent!</h4>
                        <p class="text-muted mb-4">We've sent a password reset link to your email address. Please check your inbox and follow the instructions.</p>
                        
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Didn't receive the email?</strong> Check your spam folder or 
                            <a href="#" id="resendLink" class="alert-link">click here to resend</a>.
                        </div>
                        
                        <div class="back-to-login">
                            <a href="login.html">
                                <i class="fas fa-arrow-left"></i>
                                Back to Login
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bootstrap JS -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js" integrity="sha384-ndDqU0Gzau9qJ1lfW4pNLlhNTkCfHzAVBReH9diLvGRem5+R9g2FzA8ZGN954O5Q" crossorigin="anonymous"></script>
        
        <script>
            // Forgot password form handler
            document.getElementById('forgotPasswordForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const email = document.getElementById('resetEmail').value;
                
                // Show loading state
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
                submitBtn.disabled = true;
                
                // Simulate sending email
                setTimeout(() => {
                    if (email) {
                        // Hide form and show success message
                        document.getElementById('resetForm').style.display = 'none';
                        document.getElementById('successMessage').style.display = 'block';
                    } else {
                        alert('Please enter a valid email address');
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }
                }, 2000);
            });
            
            // Resend link handler
            document.getElementById('resendLink').addEventListener('click', function(e) {
                e.preventDefault();
                
                // Show loading state
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
                
                setTimeout(() => {
                    this.innerHTML = 'Email sent again!';
                    this.style.pointerEvents = 'none';
                    this.style.color = '#10b981';
                }, 1500);
            });
        </script>
    </body>
</html>
