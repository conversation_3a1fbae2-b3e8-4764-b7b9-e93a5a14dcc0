<!doctype html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>Login - CRM Admin Panel</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">
        
        <!-- Custom CSS -->
        <link rel="stylesheet" href="style.css">
        
        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
        
        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .login-container {
                max-width: 500px;
                width: 100%;
                padding: 2rem;
            }
            
            .login-card {
                background: white;
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                overflow: hidden;
                border: none;
            }
            
            .login-header {
                background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
                color: white;
                text-align: center;
                padding: 2rem;
            }
            
            .login-header h2 {
                margin: 0;
                font-weight: 700;
            }
            
            .login-header p {
                margin: 0.5rem 0 0;
                opacity: 0.9;
            }
            
            .login-body {
                padding: 2rem;
            }
            
            .form-floating {
                margin-bottom: 1rem;
            }
            
            .form-floating .form-control {
                border: 2px solid #e5e7eb;
                border-radius: 12px;
                padding: 1rem 0.75rem;
                height: auto;
            }
            
            .form-floating .form-control:focus {
                border-color: var(--primary-color);
                box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            }
            
            .form-floating label {
                padding: 1rem 0.75rem;
                color: #6b7280;
            }
            
            .btn-login {
                background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
                border: none;
                border-radius: 12px;
                padding: 1rem;
                font-weight: 600;
                width: 100%;
                color: white;
                transition: all 0.3s ease;
            }
            
            .btn-login:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 20px rgba(79, 70, 229, 0.3);
                color: white;
            }
            
            .divider {
                text-align: center;
                margin: 1.5rem 0;
                position: relative;
            }
            
            .divider::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 0;
                right: 0;
                height: 1px;
                background: #e5e7eb;
            }
            
            .divider span {
                background: white;
                padding: 0 1rem;
                color: #6b7280;
                font-size: 0.9rem;
            }
            
            .social-login {
                display: flex;
                gap: 1rem;
                margin-bottom: 1.5rem;
            }
            
            .btn-social {
                flex: 1;
                padding: 0.75rem;
                border: 2px solid #e5e7eb;
                border-radius: 12px;
                background: white;
                color: #6b7280;
                transition: all 0.3s ease;
            }
            
            .btn-social:hover {
                border-color: var(--primary-color);
                color: var(--primary-color);
                transform: translateY(-1px);
            }
            
            .forgot-password {
                text-align: center;
                margin-top: 1rem;
            }
            
            .forgot-password a {
                color: var(--primary-color);
                text-decoration: none;
                font-weight: 500;
            }
            
            .forgot-password a:hover {
                text-decoration: underline;
            }
            
            .password-toggle {
                position: absolute;
                right: 1rem;
                top: 50%;
                transform: translateY(-50%);
                background: none;
                border: none;
                color: #6b7280;
                cursor: pointer;
                z-index: 10;
            }
            
            .password-field {
                position: relative;
            }
        </style>
    </head>
    <body>
        <div class="login-container">
            <div class="card login-card">
                <div class="login-header">
                    <i class="fas fa-chart-line fa-3x mb-3"></i>
                    <h2>CRM Admin</h2>
                    <p>Sign in to your account</p>
                </div>
                
                <div class="login-body">
                    <form id="loginForm">
                        <div class="form-floating">
                            <input type="email" class="form-control" id="email" placeholder="<EMAIL>" required>
                            <label for="email"><i class="fas fa-envelope me-2"></i>Email address</label>
                        </div>
                        
                        <div class="form-floating password-field">
                            <input type="password" class="form-control" id="password" placeholder="Password" required>
                            <label for="password"><i class="fas fa-lock me-2"></i>Password</label>
                            <button type="button" class="password-toggle" id="togglePassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">
                                Remember me
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-login">
                            <i class="fas fa-sign-in-alt me-2"></i>Sign In
                        </button>
                    </form>
                    
                    <div class="divider">
                        <span>or continue with</span>
                    </div>
                    
                    <div class="social-login">
                        <button class="btn btn-social">
                            <i class="fab fa-google"></i>
                        </button>
                        <button class="btn btn-social">
                            <i class="fab fa-microsoft"></i>
                        </button>
                        <button class="btn btn-social">
                            <i class="fab fa-apple"></i>
                        </button>
                    </div>
                    
                    <div class="forgot-password">
                        <a href="forgot-password.html">Forgot your password?</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bootstrap JS -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js" integrity="sha384-ndDqU0Gzau9qJ1lfW4pNLlhNTkCfHzAVBReH9diLvGRem5+R9g2FzA8ZGN954O5Q" crossorigin="anonymous"></script>
        
        <script>
            // Login form handler
            document.getElementById('loginForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                
                // Show loading state
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Signing in...';
                submitBtn.disabled = true;
                
                // Simulate login process
                setTimeout(() => {
                    if (email && password) {
                        // Redirect to dashboard
                        window.location.href = 'index.html';
                    } else {
                        alert('Please enter valid credentials');
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }
                }, 1500);
            });
            
            // Password toggle
            document.getElementById('togglePassword').addEventListener('click', function() {
                const passwordInput = document.getElementById('password');
                const icon = this.querySelector('i');
                
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    passwordInput.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        </script>
    </body>
</html>
