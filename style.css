/* Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Root Variables */
:root {
    --primary-color: #4f46e5;
    --primary-dark: #3730a3;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    --dark-color: #1f2937;
    --light-color: #f9fafb;
    --white-color: #ffffff;
    --border-color: #e5e7eb;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --navbar-height: 70px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--light-color);
    color: var(--dark-color);
    line-height: 1.6;
    overflow-x: hidden;
}

a {
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
}

.wrapper {
    display: flex;
    min-height: 100vh;
}

/* Login Modal Styles */
.login-modal {
    border: none;
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
}

.login-modal .modal-header {
    padding: 2rem 2rem 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border-radius: 16px 16px 0 0;
}

.login-modal .modal-body {
    padding: 2rem;
}

.login-modal .form-control {
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    transition: var(--transition);
}

.login-modal .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.login-modal .input-group-text {
    border: 2px solid var(--border-color);
    border-right: none;
    background-color: var(--light-color);
    color: var(--secondary-color);
}

.login-modal .btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border: none;
    border-radius: 8px;
    padding: 12px;
    font-weight: 600;
    transition: var(--transition);
}

.login-modal .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

/* Sidebar Styles */
#sidebar {
    width: var(--sidebar-width);
    min-width: var(--sidebar-width);
    background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
    color: white;
    transition: var(--transition);
    position: fixed;
    height: 100vh;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    box-shadow: var(--shadow-lg);
}

#sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
    min-width: var(--sidebar-collapsed-width);
}

.sidebar-scrollable {
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.sidebar-scrollable::-webkit-scrollbar {
    width: 6px;
}

.sidebar-scrollable::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar-scrollable::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: var(--navbar-height);
}

.sidebar-logo {
    display: flex;
    align-items: center;
    font-size: 1.25rem;
    font-weight: 700;
    color: white;
    transition: var(--transition);
}

#sidebar.collapsed .sidebar-logo span {
    display: none;
}

.toggle-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    cursor: pointer;
}

.toggle-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.sidebar-content {
    flex: 1;
    padding: 1rem 0;
}

.sidebar-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-item {
    margin-bottom: 0.5rem;
}

.sidebar-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    transition: var(--transition);
    position: relative;
    border-radius: 0;
}

.sidebar-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(4px);
}

.sidebar-link.active {
    background: linear-gradient(90deg, var(--primary-color), transparent);
    color: white;
    border-left: 4px solid var(--primary-color);
}

.sidebar-link i {
    width: 20px;
    margin-right: 1rem;
    text-align: center;
    font-size: 1.1rem;
}

#sidebar.collapsed .sidebar-text {
    display: none;
}

.dropdown-icon {
    margin-left: auto;
    transition: var(--transition);
    font-size: 0.8rem;
}

.has-dropdown[aria-expanded="true"] .dropdown-icon {
    transform: rotate(180deg);
}

.sidebar-submenu {
    list-style: none;
    padding: 0;
    margin: 0;
    background: rgba(0, 0, 0, 0.2);
}

.sidebar-subitem {
    margin: 0;
}

.sidebar-sublink {
    display: flex;
    align-items: center;
    padding: 0.5rem 1.5rem 0.5rem 3.5rem;
    color: rgba(255, 255, 255, 0.7);
    transition: var(--transition);
    font-size: 0.9rem;
}

.sidebar-sublink:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.sidebar-sublink i {
    width: 16px;
    margin-right: 0.75rem;
    font-size: 0.9rem;
}

.sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.user-info {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

#sidebar.collapsed .user-info {
    justify-content: center;
}

.user-avatar img {
    width: 40px;
    height: 40px;
    object-fit: cover;
}

.user-details {
    margin-left: 0.75rem;
    flex: 1;
}

#sidebar.collapsed .user-details {
    display: none;
}

.user-name {
    display: block;
    font-weight: 600;
    font-size: 0.9rem;
    color: white;
}

.user-role {
    display: block;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
}

.logout-link {
    justify-content: center;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 8px;
    color: #fca5a5;
}

.logout-link:hover {
    background: rgba(239, 68, 68, 0.2);
    color: #f87171;
    transform: none;
}

/* Main Content Styles */
.main {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: var(--light-color);
}

#sidebar.collapsed + .main {
    margin-left: var(--sidebar-collapsed-width);
}

/* Top Navbar */
.navbar {
    height: var(--navbar-height);
    box-shadow: var(--shadow);
    background: var(--white-color) !important;
    border-bottom: 1px solid var(--border-color) !important;
    padding: 0 2rem;
}

.navbar .input-group .form-control {
    border: 2px solid var(--border-color);
    border-radius: 8px 0 0 8px;
    transition: var(--transition);
}

.navbar .input-group .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: none;
}

.navbar .btn-outline-secondary {
    border: 2px solid var(--border-color);
    border-left: none;
    border-radius: 0 8px 8px 0;
    color: var(--secondary-color);
}

.nav-link {
    color: var(--secondary-color) !important;
    transition: var(--transition);
    padding: 0.5rem 1rem;
    border-radius: 8px;
}

.nav-link:hover {
    color: var(--primary-color) !important;
    background: rgba(79, 70, 229, 0.1);
}

.notification-dropdown {
    min-width: 300px;
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: 12px;
}

.notification-dropdown .dropdown-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.notification-dropdown .dropdown-item:hover {
    background: var(--light-color);
}

/* Content Wrapper */
.content-wrapper {
    flex: 1;
    padding: 0;
}

/* Breadcrumb */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: var(--secondary-color);
}

/* Stats Cards */
.stats-card {
    transition: var(--transition);
    border-radius: 12px;
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

/* Activity Timeline */
.activity-timeline {
    position: relative;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    position: relative;
}

.activity-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 20px;
    top: 50px;
    width: 2px;
    height: calc(100% - 20px);
    background: var(--border-color);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
    flex-shrink: 0;
    z-index: 1;
    position: relative;
}

.activity-content {
    flex: 1;
    padding-top: 0.25rem;
}

/* Cards */
.card {
    border-radius: 12px;
    border: none;
    transition: var(--transition);
}

.card:hover {
    box-shadow: var(--shadow-lg);
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
    padding: 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-info {
    background: var(--info-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

/* Footer */
.footer {
    margin-top: auto;
    padding: 1rem 0;
    border-top: 1px solid var(--border-color);
    background: var(--white-color);
}

/* Responsive Design */
@media (max-width: 1199.98px) {
    :root {
        --sidebar-width: 260px;
    }
}

@media (max-width: 991.98px) {
    #sidebar {
        transform: translateX(-100%);
        position: fixed;
        z-index: 1050;
    }

    #sidebar.show {
        transform: translateX(0);
    }

    .main {
        margin-left: 0;
    }

    .navbar {
        padding: 0 1rem;
    }

    .navbar .input-group {
        width: 200px !important;
    }
}

@media (max-width: 767.98px) {
    .navbar {
        padding: 0 0.5rem;
    }

    .navbar .input-group {
        display: none;
    }

    .container-fluid {
        padding: 1rem !important;
    }

    .stats-card {
        margin-bottom: 1rem;
    }

    .user-details span {
        display: none;
    }
}

@media (max-width: 575.98px) {
    .card-body {
        padding: 1rem;
    }

    .card-header {
        padding: 1rem;
    }

    .activity-item {
        margin-bottom: 1rem;
    }

    .activity-icon {
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }

    .stats-icon {
        width: 48px;
        height: 48px;
        font-size: 1.2rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #111827;
        --white-color: #1f2937;
        --dark-color: #f9fafb;
        --border-color: #374151;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}