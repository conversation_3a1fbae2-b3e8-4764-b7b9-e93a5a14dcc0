/* Google outfit font */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@100..900&family=Outfit:wght@100..900&display=swap');

a {
    text-decoration: none;
}

body {
    font-family : 'Outfit', sans-serif;
}

.wrapper{
    display: flex;
}

.main{
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    width: 100%;
    overflow: hidden;
    transition: all 0.25s ease-in-out;
    background-color: #fff;
}

#sidebar {
    width: 90px;
    min-width: 90px;
    transition: all 0.25s ease-in-out;
    background-color: #0b0f19;
    display: flex;
    flex-direction: column;
}

#sidebar.expand {
    width: 260px;
    min-width: 260px;
}

#sidebar:not(.expand) .sidebar-logo {
    display: none;
}   

.toggle-btn {
    width: 30px;
    height: 30px;
    color: #FFF;
    border-radius: o.425rem;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #323c55;
}

li {
    list-style: none;
}