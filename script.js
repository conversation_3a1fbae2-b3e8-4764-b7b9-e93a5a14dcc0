// Modern CRM Admin Panel JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the application
    initializeApp();
});

function initializeApp() {
    // Show login modal on page load
    showLoginModal();

    // Initialize sidebar functionality
    initializeSidebar();

    // Initialize form handlers
    initializeForms();

    // Initialize responsive features
    initializeResponsive();

    // Initialize tooltips and other Bootstrap components
    initializeBootstrapComponents();
}

// Login Modal Functions
function showLoginModal() {
    const loginModal = new bootstrap.Modal(document.getElementById('loginModal'), {
        backdrop: 'static',
        keyboard: false
    });
    loginModal.show();
}

function hideLoginModal() {
    const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
    if (loginModal) {
        loginModal.hide();
    }
}

// Sidebar Functions
function initializeSidebar() {
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebarToggle');
    const toggleIcon = document.getElementById('toggleIcon');
    const mobileSidebarToggle = document.getElementById('mobileSidebarToggle');

    // Desktop sidebar toggle
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');

            // Update toggle icon
            if (sidebar.classList.contains('collapsed')) {
                toggleIcon.classList.remove('fa-bars');
                toggleIcon.classList.add('fa-arrow-right');
            } else {
                toggleIcon.classList.remove('fa-arrow-right');
                toggleIcon.classList.add('fa-bars');
            }
        });
    }

    // Mobile sidebar toggle
    if (mobileSidebarToggle) {
        mobileSidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
        });
    }

    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(event) {
        if (window.innerWidth <= 991.98) {
            if (!sidebar.contains(event.target) && !mobileSidebarToggle.contains(event.target)) {
                sidebar.classList.remove('show');
            }
        }
    });

    // Handle dropdown menus in sidebar
    const dropdownLinks = document.querySelectorAll('.sidebar-link.has-dropdown');
    dropdownLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Close other dropdowns
            dropdownLinks.forEach(otherLink => {
                if (otherLink !== link) {
                    const otherTarget = otherLink.getAttribute('data-bs-target');
                    const otherSubmenu = document.querySelector(otherTarget);
                    if (otherSubmenu && otherSubmenu.classList.contains('show')) {
                        bootstrap.Collapse.getInstance(otherSubmenu).hide();
                    }
                }
            });
        });
    });
}

// Form Handlers
function initializeForms() {
    // Login form
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }

    // Forgot password form
    const forgotPasswordForm = document.getElementById('forgotPasswordForm');
    if (forgotPasswordForm) {
        forgotPasswordForm.addEventListener('submit', handleForgotPassword);
    }

    // Password toggle
    const togglePassword = document.getElementById('togglePassword');
    if (togglePassword) {
        togglePassword.addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    }

    // Logout handlers
    const logoutBtn = document.getElementById('logoutBtn');
    const navLogoutBtn = document.getElementById('navLogoutBtn');

    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }

    if (navLogoutBtn) {
        navLogoutBtn.addEventListener('click', handleLogout);
    }
}

// Login Handler
function handleLogin(e) {
    e.preventDefault();

    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const rememberMe = document.getElementById('rememberMe').checked;

    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Logging in...';
    submitBtn.disabled = true;

    // Simulate API call
    setTimeout(() => {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        // Validate credentials (demo purposes)
        if (email && password) {
            // Hide login modal
            hideLoginModal();

            // Show success message
            showNotification('Login successful! Welcome to CRM Admin Panel.', 'success');

            // Store user session (demo)
            if (rememberMe) {
                localStorage.setItem('crmUser', JSON.stringify({
                    email: email,
                    name: 'John Doe',
                    role: 'Administrator'
                }));
            }
        } else {
            showNotification('Please enter valid credentials.', 'error');
        }
    }, 1500);
}

// Forgot Password Handler
function handleForgotPassword(e) {
    e.preventDefault();

    const email = document.getElementById('resetEmail').value;

    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
    submitBtn.disabled = true;

    // Simulate API call
    setTimeout(() => {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        if (email) {
            showNotification('Password reset link sent to your email!', 'success');

            // Close modal after short delay
            setTimeout(() => {
                const forgotModal = bootstrap.Modal.getInstance(document.getElementById('forgotPasswordModal'));
                if (forgotModal) {
                    forgotModal.hide();
                }
            }, 1000);
        } else {
            showNotification('Please enter a valid email address.', 'error');
        }
    }, 1500);
}

// Logout Handler
function handleLogout(e) {
    e.preventDefault();

    // Show confirmation
    if (confirm('Are you sure you want to logout?')) {
        // Clear user session
        localStorage.removeItem('crmUser');

        // Show login modal
        showLoginModal();

        // Show logout message
        showNotification('You have been logged out successfully.', 'info');
    }
}

// Responsive Functions
function initializeResponsive() {
    // Handle window resize
    window.addEventListener('resize', function() {
        const sidebar = document.getElementById('sidebar');

        // Auto-collapse sidebar on small screens
        if (window.innerWidth <= 991.98) {
            sidebar.classList.remove('show');
        }

        // Auto-expand sidebar on large screens
        if (window.innerWidth > 1199.98) {
            sidebar.classList.remove('collapsed');
        }
    });

    // Initialize responsive behavior
    if (window.innerWidth <= 991.98) {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.remove('show');
    }
}

// Bootstrap Components
function initializeBootstrapComponents() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

// Notification System
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${getBootstrapAlertClass(type)} alert-dismissible fade show notification-toast`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border: none;
        border-radius: 8px;
    `;

    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas ${getNotificationIcon(type)} me-2"></i>
            <span>${message}</span>
            <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

function getBootstrapAlertClass(type) {
    const classes = {
        'success': 'success',
        'error': 'danger',
        'warning': 'warning',
        'info': 'info'
    };
    return classes[type] || 'info';
}

function getNotificationIcon(type) {
    const icons = {
        'success': 'fa-check-circle',
        'error': 'fa-exclamation-circle',
        'warning': 'fa-exclamation-triangle',
        'info': 'fa-info-circle'
    };
    return icons[type] || 'fa-info-circle';
}

// Utility Functions
function animateValue(element, start, end, duration) {
    let startTimestamp = null;
    const step = (timestamp) => {
        if (!startTimestamp) startTimestamp = timestamp;
        const progress = Math.min((timestamp - startTimestamp) / duration, 1);
        const value = Math.floor(progress * (end - start) + start);
        element.textContent = value.toLocaleString();
        if (progress < 1) {
            window.requestAnimationFrame(step);
        }
    };
    window.requestAnimationFrame(step);
}

// Initialize counter animations when elements come into view
function initializeCounterAnimations() {
    const counters = document.querySelectorAll('.stats-card h3');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = entry.target;
                const value = parseInt(target.textContent.replace(/,/g, ''));
                animateValue(target, 0, value, 2000);
                observer.unobserve(target);
            }
        });
    });

    counters.forEach(counter => {
        observer.observe(counter);
    });
}

// Search functionality
function initializeSearch() {
    const searchInput = document.querySelector('.navbar .form-control');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            const query = e.target.value.toLowerCase();
            // Implement search logic here
            console.log('Searching for:', query);
        });
    }
}

// Quick Actions
function initializeQuickActions() {
    const quickActionBtns = document.querySelectorAll('.card .btn');
    quickActionBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const action = this.textContent.trim();
            showNotification(`${action} feature will be implemented soon!`, 'info');
        });
    });
}

// Initialize additional features when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Delay initialization of some features
    setTimeout(() => {
        initializeCounterAnimations();
        initializeSearch();
        initializeQuickActions();
    }, 1000);

    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 100);
    });
});

// Handle page visibility changes
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        console.log('Page is hidden');
    } else {
        console.log('Page is visible');
        // Refresh data when page becomes visible
    }
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + K for search
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        const searchInput = document.querySelector('.navbar .form-control');
        if (searchInput) {
            searchInput.focus();
        }
    }

    // Escape to close modals
    if (e.key === 'Escape') {
        const sidebar = document.getElementById('sidebar');
        if (window.innerWidth <= 991.98 && sidebar.classList.contains('show')) {
            sidebar.classList.remove('show');
        }
    }
});